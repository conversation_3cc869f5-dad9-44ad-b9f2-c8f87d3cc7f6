import { cloneDeep } from 'lodash'

// 菜品接口
export interface MenuItem {
  id: string
  name: string
  description?: string
  price: number
  originalPrice?: number
  image?: string
  categoryId: string
  isAvailable: boolean
  sortOrder: number
  createTime?: number
  updateTime?: number
}

// 菜单接口
export interface Menu {
  id: string
  name: string
  description?: string
  items: MenuItem[]
  isActive: boolean
  createTime?: number
  updateTime?: number
}

// 菜单管理状态接口
export interface MenuManageState {
  // 菜单列表
  menuList: Menu[]
  // 当前菜品
  dish: MenuItem | null
  // 菜单分组数据（用于 MenuEditor 组件的数据恢复）
  menuGroups: any[] // 使用 any 类型避免循环依赖，实际类型为 IMenuGroupListExtended[]
  specialtyDishes: any[] // 使用 any 类型避免循环依赖，实际类型为 IDishList[]
  // 删除记录（用于数据合并时过滤）
  deletedGroupIds: Set<string>
  deletedDishIds: Set<string>
}

// 获取默认状态
const getDefaultState = (): MenuManageState => ({
  menuList: [],
  dish: null,
  menuGroups: [],
  specialtyDishes: [],
  deletedGroupIds: new Set<string>(),
  deletedDishIds: new Set<string>(),
})

// 状态
const state: MenuManageState = getDefaultState()

// 变更接口
interface MenuManageMutations {
  // 菜单相关
  SET_MENU_LIST(state: MenuManageState, menuList: Menu[]): void
  ADD_MENU(state: MenuManageState, menu: Menu): void
  UPDATE_MENU(state: MenuManageState, menu: Menu): void
  REMOVE_MENU(state: MenuManageState, menuId: string): void

  // 菜品相关
  SET_DISH(state: MenuManageState, dish: MenuItem | null): void
  UPDATE_DISH(state: MenuManageState, dish: MenuItem): void
  CLEAR_DISH(state: MenuManageState): void

  // 菜单分组相关
  SET_MENU_GROUPS(state: MenuManageState, menuGroups: any[]): void
  SET_SPECIALTY_DISHES(state: MenuManageState, specialtyDishes: any[]): void
  CLEAR_MENU_GROUPS(state: MenuManageState): void

  // 删除记录相关
  ADD_DELETED_GROUP_ID(state: MenuManageState, groupId: string): void
  ADD_DELETED_DISH_ID(state: MenuManageState, dishId: string): void
  REMOVE_DELETED_GROUP_ID(state: MenuManageState, groupId: string): void
  REMOVE_DELETED_DISH_ID(state: MenuManageState, dishId: string): void
  CLEAR_DELETED_RECORDS(state: MenuManageState): void

  // 重置状态
  RESET_STATE(state: MenuManageState): void
}

// 变更实现
const mutations: MenuManageMutations = {
  // 菜单相关
  SET_MENU_LIST(state, menuList) {
    state.menuList = cloneDeep(menuList)
  },

  ADD_MENU(state, menu) {
    state.menuList.push(cloneDeep(menu))
  },

  UPDATE_MENU(state, menu) {
    const index = state.menuList.findIndex(m => m.id === menu.id)
    if (index !== -1) {
      state.menuList[index] = cloneDeep(menu)
    }
  },

  REMOVE_MENU(state, menuId) {
    state.menuList = state.menuList.filter(menu => menu.id !== menuId)
  },

  // 菜品相关
  SET_DISH(state, dish) {
    state.dish = dish ? cloneDeep(dish) : null
  },

  UPDATE_DISH(state, dish) {
    state.dish = cloneDeep(dish)
  },

  CLEAR_DISH(state) {
    state.dish = null
  },

  // 菜单分组相关
  SET_MENU_GROUPS(state, menuGroups) {
    state.menuGroups = cloneDeep(menuGroups)
  },

  SET_SPECIALTY_DISHES(state, specialtyDishes) {
    state.specialtyDishes = cloneDeep(specialtyDishes)
  },

  CLEAR_MENU_GROUPS(state) {
    state.menuGroups = []
    state.specialtyDishes = []
  },

  // 删除记录相关
  ADD_DELETED_GROUP_ID(state, groupId) {
    state.deletedGroupIds.add(groupId)
  },

  ADD_DELETED_DISH_ID(state, dishId) {
    state.deletedDishIds.add(dishId)
  },

  REMOVE_DELETED_GROUP_ID(state, groupId) {
    state.deletedGroupIds.delete(groupId)
  },

  REMOVE_DELETED_DISH_ID(state, dishId) {
    state.deletedDishIds.delete(dishId)
  },

  CLEAR_DELETED_RECORDS(state) {
    state.deletedGroupIds.clear()
    state.deletedDishIds.clear()
  },

  // 重置状态
  RESET_STATE(state) {
    Object.assign(state, getDefaultState())
  },
}

// 动作接口
interface MenuManageActions {
  // 菜单相关
  setMenuList(context: { commit: Function }, menuList: Menu[]): void
  addMenu(context: { commit: Function }, menu: Menu): void
  updateMenu(context: { commit: Function }, menu: Menu): void
  removeMenu(context: { commit: Function }, menuId: string): void

  // 菜品相关
  setDish(context: { commit: Function }, dish: MenuItem | null): void
  updateDish(context: { commit: Function }, dish: MenuItem): void
  clearDish(context: { commit: Function }): void

  // 菜单分组相关
  setMenuGroups(context: { commit: Function }, menuGroups: any[]): void
  setSpecialtyDishes(context: { commit: Function }, specialtyDishes: any[]): void
  clearMenuGroups(context: { commit: Function }): void

  // 删除记录相关
  addDeletedGroupId(context: { commit: Function }, groupId: string): void
  addDeletedDishId(context: { commit: Function }, dishId: string): void
  removeDeletedGroupId(context: { commit: Function }, groupId: string): void
  removeDeletedDishId(context: { commit: Function }, dishId: string): void
  clearDeletedRecords(context: { commit: Function }): void

  // 重置状态
  resetState(context: { commit: Function }): void
}

// 动作实现
const actions: MenuManageActions = {
  // 菜单相关
  setMenuList({ commit }, menuList) {
    commit('SET_MENU_LIST', menuList)
  },

  addMenu({ commit }, menu) {
    commit('ADD_MENU', menu)
  },

  updateMenu({ commit }, menu) {
    commit('UPDATE_MENU', menu)
  },

  removeMenu({ commit }, menuId) {
    commit('REMOVE_MENU', menuId)
  },

  // 菜品相关
  setDish({ commit }, dish) {
    commit('SET_DISH', dish)
  },

  updateDish({ commit }, dish) {
    commit('UPDATE_DISH', dish)
  },

  clearDish({ commit }) {
    commit('CLEAR_DISH')
  },

  // 菜单分组相关
  setMenuGroups({ commit }, menuGroups) {
    commit('SET_MENU_GROUPS', menuGroups)
  },

  setSpecialtyDishes({ commit }, specialtyDishes) {
    commit('SET_SPECIALTY_DISHES', specialtyDishes)
  },

  clearMenuGroups({ commit }) {
    commit('CLEAR_MENU_GROUPS')
  },

  // 删除记录相关
  addDeletedGroupId({ commit }, groupId) {
    commit('ADD_DELETED_GROUP_ID', groupId)
  },

  addDeletedDishId({ commit }, dishId) {
    commit('ADD_DELETED_DISH_ID', dishId)
  },

  removeDeletedGroupId({ commit }, groupId) {
    commit('REMOVE_DELETED_GROUP_ID', groupId)
  },

  removeDeletedDishId({ commit }, dishId) {
    commit('REMOVE_DELETED_DISH_ID', dishId)
  },

  clearDeletedRecords({ commit }) {
    commit('CLEAR_DELETED_RECORDS')
  },

  // 重置状态
  resetState({ commit }) {
    commit('RESET_STATE')
  },
}

// 获取器接口
interface MenuManageGetters {
  // 菜单相关
  menuList: (state: MenuManageState) => Menu[]

  // 菜品相关
  dish: (state: MenuManageState) => MenuItem | null
  getMenuItemsExcludingCurrent: (state: MenuManageState) => (currentItemId?: string) => MenuItem[]

  // 菜单分组相关
  menuGroups: (state: MenuManageState) => any[]
  specialtyDishes: (state: MenuManageState) => any[]

  // 删除记录相关
  deletedGroupIds: (state: MenuManageState) => Set<string>
  deletedDishIds: (state: MenuManageState) => Set<string>
}

// 获取器实现
const getters: MenuManageGetters = {
  // 菜单相关
  menuList: state => state.menuList,

  // 菜品相关
  dish: state => state.dish,

  // 获取所有菜品，排除当前编辑的菜品
  getMenuItemsExcludingCurrent: state => (currentItemId?: string) => {
    const items: MenuItem[] = []
    state.menuList.forEach(menu => {
      menu.items.forEach(item => {
        if (!currentItemId || item.id !== currentItemId) {
          items.push(item)
        }
      })
    })
    return items
  },

  // 菜单分组相关
  menuGroups: state => state.menuGroups,
  specialtyDishes: state => state.specialtyDishes,

  // 删除记录相关
  deletedGroupIds: state => state.deletedGroupIds,
  deletedDishIds: state => state.deletedDishIds,
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
}
